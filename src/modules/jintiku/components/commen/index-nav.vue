<template>
	<view :class="needProcess?'index-nav':'index-nav-white'">
		<view v-if="page_type == 'home'" class="chapter-exercise" @click="goDetail2(info)">
			<view class="test-paper-process">
				<view class="test-paper-title">
					<view class="paper-title">{{info.name}}</view>
				</view>
				<view class="papaer-tag">
					<view class="plain-tag">共{{info.question_number}}道题目</view>
					<view class="custom-tag">有效期:{{info.year}}</view>
				</view>
				<view class="bottom-row">
					<view class="process-row" v-if="needProcess">
						<view class="process">
							<view class="current" :style="{
						  width: num + '%'
						}"></view>
						</view>
						<view class="process-text">
							<!-- <view class="process-success"> 已完成{{ 40 }}% </view> -->
							<view class="process-static">
								{{ info.do_question_num }}/{{ info.question_number }}
							</view>
						</view>
					</view>
					<view class="action-section" v-if="needProcess">
					  <view
					    class="check-in-btn"
					    @click="handleCheckIn"
					  >
					    <text class="btn-text">立即刷题</text>
					  </view>
					</view>
					<view class="start-time" v-else>
						开考时间:2025-04-28 00:00:00
					</view>
				</view>
			</view>
		</view>
		<view style="height: 32rpx"></view>
		<!-- <view class="navs" v-if="false">
			<view class="nav" v-for="(item, i) in navs" :key="i">
				<image :src="item.img" mode="widthFix" @click="goDetail(item.url)" />
				<text @click="goDetail(item.url)">{{ item.name }}</text>
			</view>
		</view> -->
	</view>
</template>
<script>
	import {
		index,
		chapterpackage,
		getOrderV2
	} from '../../api/index'
	import {
		app_id
	} from '../../config'
	import {
		goToLogin,
		setSubscribeMessage
	} from '../../utils/index'
	export default {
		name: 'IndexNav',
		components: {
			// 如果需要局部注册 u-tag 组件，可以在这里添加
		},
		props: {
			page_type: {
				type: String,
				default: 'home'
			},
			needProcess: {
				type: Boolean,
				default: true
			},
			pageTitle: {
				type: String,
				default: '口腔实践技能题目'
			},
			
		},
		data() {
			return {
				
				info: {
					name:'',
					collect_question_num: '0',
					do_question_num: '0', // 已做题数
					err_question_num: '0', // 错题
					question_number: '0' // 章节题目数
				}
			}
		},
		created() {
			this.init()
		},
		computed: {
			num() {
				if (this.info?.do_question_num == 0) {
					return 0
				} else {
					return (
						(this.info.do_question_num / this.info.question_number) *
						100
					).toFixed(2)
				}
			}
		},
		methods: {
			goDetail2(item) {
				if (!this.$store.state.jintiku.token) {
					console.log('没有', this.$store.state.jintiku.token)
					goToLogin()
					return
				}
				setSubscribeMessage('login')
				if (item.id == '0' || !item.id) {
					this.$xh.Toast('暂无免费章节练习！')
					return
				}
				if (item.permission_status != '1') {
					this.getOrder()
				}
				this.$xh.push(
					'jintiku',
					`pages/chapterExercise/index?professional_id=${item.professional_id}&goods_id=${item.id}&total=${this.info.question_number}&isfree=1`
				)
			},
			//支付点击
			async getOrder() {
				let payable_amount = 0
				let student_id = uni.getStorageSync('__xingyun_userinfo__').student_id
				let employee_id = this.$store.state.jintiku.employee_id
				let goods_id = this.info.id
				let data = {
					business_scene: 1,
					goods: [{
						goods_id: goods_id,
						// goods_months_price_id: '',
						// months: this.info.month,
						class_campus_id: '',
						class_city_id: '',
						goods_num: '1'
					}],
					deposit_amount: Number(payable_amount),
					payable_amount: Number(payable_amount),
					real_amount: Number(payable_amount),
					remark: '',
					student_adddatas_id: '',
					student_id: student_id,
					total_amount: Number(payable_amount),
					app_id: app_id,
					pay_method: '',
					order_type: 10,
					discount_amount: 0,
					coupons_ids: [],
					employee_id: employee_id || '508948528815416786',
					delivery_type: 1 // 默认总部邮寄
				}
				getOrderV2({
					...data
				}).then(res => {
					this.info.permission_status = 1
				})
			},
			isLogin() {
				return !!this.$store.state.jintiku.token
			},
			init() {
				
				// if (!this.isLogin()) {
				//   return
				// }

				chapterpackage({
					professional_id: uni.getStorageSync('__xingyun_major__').major_id || '',
					noloading: true
				}).then(res => {
					this.info = {
						name:res.data.name,
						collect_question_num: '0',
						do_question_num: res.data.student_finish.do_num, // 已做题数
						err_question_num: '0', // 错题
						question_number: res.data.question_num, // 章节题目数
						...res.data
					}
				})
				// index
				//   .static({
				//     noloading: true
				//   })
				//   .then(data => {
				//     this.info = data.data
				//   })
			},
			handleCheckIn(){
				
			},
			goDetail(url) {
				if (!this.isLogin()) {
					// 去登录
					// this.$xh.push('jintiku', 'pages/loginCenter/index')
					goToLogin('goToLogin11')
					return
				}
				this.$xh.push('jintiku', url)
			}
		}
	}
</script>
<style scoped lang="less">
	.index-nav {
		padding-bottom: 20rpx;
		border-radius: 32rpx;
		background: url(https://xy-shunshun-pro.oss-cn-hangzhou.aliyuncs.com/cell-banckgroud-img.png);
	}
	
	.index-nav-white{
		padding-bottom: 20rpx;
		background-color: white;
		border-radius: 32rpx;
	}

	.chapter-exercise {
		height: 180rpx;
		display: flex;
		padding: 40rpx 32rpx;
		border-radius: 32rpx;

		.testPaperImg {
			width: 80rpx;
			height: 100rpx;
			margin-right: 32rpx;
		}

		.test-paper-process {
			flex: 1;

			.test-paper-title {
				display: flex;
				align-items: center;
				justify-content: space-between;
				margin-bottom: 14rpx;

				.paper-title {
					font-size: 30rpx;
					font-family: PingFangSC-Medium, PingFang SC;
					color: #161f30;
					line-height: 30rpx;
				}
			}

			.papaer-tag {
				display: flex;
				flex-direction: row;
				width: 100%;
				margin-top: 20rpx;

				.plain-tag {
					background-color: #E0F0FF;
					color: #4783DC;
					padding: 10rpx 20rpx;
					border-radius: 8rpx;
					font-size: 20rpx;
					margin-right: 12rpx;
				}

				.custom-tag {
					background-color: #EDF1F2;
					color: #777777;
					padding: 10rpx 20rpx;
					border-radius: 8rpx;
					font-size: 20rpx;
					margin-right: 12rpx;
				}
			}
			
			.process-row{
				display: flex;
				flex-direction: row;
				align-items: center;
				margin-top: 20rpx;
				width: 60%;
			}

			.process {
				width: 80%;
				height: 16rpx;
				background: #DDF3FD;
				border-radius: 15rpx;
				opacity: 0.6;
				position: relative;

				.current {
					background-color: #55C3FF;
					border-radius: 15rpx;
					position: absolute;
					height: 16rpx;
					left: 0;
					top: 0;
					transition: all 0.25s;
				}
			}

			.process-text {
				display: flex;
				align-items: center;
				justify-content: space-between;
				font-size: 24rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				margin-left: 20rpx;
				// color: #999999;
				color: rgba(3, 32, 61, 0.85);
			}
			
			.start-time{
				font-size: 24rpx;
				color: #777777;
				margin-top: 40rpx;
			}
		}
	}
	
	.action-section {
	  .check-in-btn {
	    background: linear-gradient(135deg, #FF860E 0%, #FF6912 100%);
	    border-radius: 35rpx;
			  width: 160rpx;
			  height:70rpx;
	    box-shadow: 0 4rpx 16rpx rgba(255, 138, 0, 0.3);
	    transition: all 0.3s ease;
			  display: flex;
			  flex-direction: row;
			  justify-content: center;
			  align-items: center;
			  
	    &:active {
	      transform: scale(0.95);
	    }
	
	    &.checked {
	      background: linear-gradient(135deg, #40B983 0%, #6BCF95 100%);
	      box-shadow: 0 4rpx 16rpx rgba(64, 185, 131, 0.3);
	    }
	
	    .btn-text {
	      font-size: 28rpx;
	      color: #fff;
	    }
	  }
	}
	
	.bottom-row{
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
		width: 100%;
	}

	.navs {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-top: 34rpx;
		padding: 0 30rpx;
		margin-bottom: 48rpx;

		.nav {
			display: flex;
			flex-direction: column;
			align-items: center;

			image {
				width: 52rpx;
				height: 52rpx;
				margin-bottom: 20rpx;
			}

			text {
				font-size: 26rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #2f465f;
				line-height: 26rpx;
			}
		}
	}
</style>