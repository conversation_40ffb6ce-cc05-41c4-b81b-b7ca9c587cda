<template>
	<view class="index-nav-white">
		<view class="chapter-exercise" @click="goDetail(info)">
			<view class="test-paper-process">
				<view class="test-paper-title">
					<view class="paper-title">{{formData?formData.name:'默认'}}</view>
				</view>
				<view class="papaer-tag">
					<view v-if="info.num_text" class="plain-tag">{{ info.num_text }}</view>
					<view class="custom-tag">{{ validity(formData) }}</view>
				</view>
				<view class="bottom-row">
					<view class="start-time">
						开考时间:{{formData.created_at}}
					</view>
				</view>
			</view>
		</view>
		<view style="height: 32rpx"></view>
	</view>
</template>
<script>
	import {
		index,
		chapterpackage,
		getOrderV2
	} from '../../api/index'
	import {
		app_id
	} from '../../config'
	import {
		goToLogin,
		setSubscribeMessage
	} from '../../utils/index'
	export default {
		name: 'IndexNavItem',
		components: {
			// 如果需要局部注册 u-tag 组件，可以在这里添加
		},
		props: {
			pageTitle: {
				type: String,
				default: '口腔实践技能题目'
			},
			formData: {
				type: Object,
				default:{}
			}
			
		},
		data() {
			return {
				info: {}
			}
		},
		created(){
			
		},
		watch:{
			formData(newvalue,oldvalue){
				this.info = this.months_prices(newvalue)
			}
		},
		methods: {
			validity(item) {
				if(!item){
					return '有效期：永久'
				}

				// 检查是否有有效期字段
				if (!item.validity_start_date || !item.validity_end_date) {
					return '有效期：永久'
				}

				if (/0001/g.test(item.validity_start_date)) {
					return '有效期：永久'
				}

				const result = `有效期：${item.validity_start_date}~${item.validity_end_date}`;
				return result;
			},
			isLogin() {
				return !!this.$store.state.jintiku.token
			},
			init() {
				
			},
			handleCheckIn(){
				
			},
			goDetail(item) {
				if (item.permission_status == '1' || item?.months_prices?.length) {
					this.$xh.push(
						'jintiku',
						`pages/test/detail?professional_id=${item.professional_id}&id=${item.id}`
					)
				} else {
					if (!this.$store.state.jintiku.token) {
						goToLogin()
						return
					}
					this.getOrder(item)
				}
				return
			},
			getOrder(item) {
				let payable_amount = 0
				let student_id = uni.getStorageSync('__xingyun_userinfo__').student_id
				let goods_id = item.id
				let employee_id = this.$store.state.jintiku.employee_id
				let data = {
					business_scene: 1,
					goods: [{
						goods_id: goods_id,
						// goods_months_price_id: '',
						// months: this.info.month,
						class_campus_id: '',
						class_city_id: '',
						goods_num: '1'
					}],
					deposit_amount: Number(payable_amount),
					payable_amount: Number(payable_amount),
					real_amount: Number(payable_amount),
					remark: '',
					student_adddatas_id: '',
					student_id: student_id,
					total_amount: Number(payable_amount),
					app_id: app_id,
					pay_method: '',
					order_type: 10,
					discount_amount: 0,
					coupons_ids: [],
					employee_id: employee_id || '508948528815416786',
					delivery_type: 1 // 默认总部邮寄
				}
				getOrderV2({
					...data
				}).then(res => {
					item.permission_status = '1'
					this.goDetail(item)
				})
			},
			months_prices(item) {
				if(!item){
					return "";
				}
				let info = item.tiku_goods_details
				let system_id_name = item?.teaching_system?.system_id_name || ''
				if (item.type == 10) {
					system_id_name = `开考时间:${info.exam_time}`
				}
			
				let num_text = `共${item.tiku_goods_details.question_num}题`
				if (item.type == 8) {
					num_text = `共${info.paper_num}份`
				}
				if (item.type == 10) {
					num_text = `共${info.exam_round_num}轮`
				}
				if (!item.months_prices || item.months_prices.length == 0) {
					return {
						sale_price: item.sale_price,
						original_price: '0',
						month_text: '永久',
						system_id_name,
						num_text
					}
				}
				let data = item.months_prices?.sort((a, b) => {
					if (a.month == 0) {
						return 1
					} else {
						return Number(a.month) - Number(b.month)
					}
				})[0]
				let sale_price = 0
				let original_price = '0'
				let month_text = ''
			
				if (data) {
					sale_price = data.sale_price
					original_price = data.original_price
					if (data.month == 0) {
						month_text = '永久'
					} else {
						month_text = data.month + '个月'
					}
				}
				return {
					sale_price,
					original_price,
					month_text,
					system_id_name,
					num_text
				}
			}
		}
	}
</script>
<style scoped lang="less">
	.index-nav {
		padding-bottom: 20rpx;
		border-radius: 32rpx;
		background: url(https://xy-shunshun-pro.oss-cn-hangzhou.aliyuncs.com/cell-banckgroud-img.png);
	}
	
	.index-nav-white{
		padding-bottom: 20rpx;
		background-color: white;
		border-radius: 32rpx;
	}

	.chapter-exercise {
		height: 180rpx;
		display: flex;
		padding: 40rpx 32rpx;
		border-radius: 32rpx;

		.testPaperImg {
			width: 80rpx;
			height: 100rpx;
			margin-right: 32rpx;
		}

		.test-paper-process {
			flex: 1;

			.test-paper-title {
				display: flex;
				align-items: center;
				justify-content: space-between;
				margin-bottom: 14rpx;

				.paper-title {
					font-size: 30rpx;
					font-family: PingFangSC-Medium, PingFang SC;
					color: #161f30;
					line-height: 30rpx;
				}
			}

			.papaer-tag {
				display: flex;
				flex-direction: row;
				width: 100%;
				margin-top: 20rpx;

				.plain-tag {
					background-color: #E0F0FF;
					color: #4783DC;
					padding: 10rpx 20rpx;
					border-radius: 8rpx;
					font-size: 20rpx;
					margin-right: 12rpx;
				}

				.custom-tag {
					background-color: #EDF1F2;
					color: #777777;
					padding: 10rpx 20rpx;
					border-radius: 8rpx;
					font-size: 20rpx;
					margin-right: 12rpx;
				}
			}
			
			.process-row{
				display: flex;
				flex-direction: row;
				align-items: center;
				margin-top: 20rpx;
				width: 60%;
			}

			.process {
				width: 80%;
				height: 16rpx;
				background: #DDF3FD;
				border-radius: 15rpx;
				opacity: 0.6;
				position: relative;

				.current {
					background-color: #55C3FF;
					border-radius: 15rpx;
					position: absolute;
					height: 16rpx;
					left: 0;
					top: 0;
					transition: all 0.25s;
				}
			}

			.process-text {
				display: flex;
				align-items: center;
				justify-content: space-between;
				font-size: 24rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				margin-left: 20rpx;
				// color: #999999;
				color: rgba(3, 32, 61, 0.85);
			}
			
			.start-time{
				font-size: 24rpx;
				color: #777777;
				margin-top: 40rpx;
			}
		}
	}
	
	.action-section {
	  .check-in-btn {
	    background: linear-gradient(135deg, #FF860E 0%, #FF6912 100%);
	    border-radius: 35rpx;
			  width: 160rpx;
			  height:70rpx;
	    box-shadow: 0 4rpx 16rpx rgba(255, 138, 0, 0.3);
	    transition: all 0.3s ease;
			  display: flex;
			  flex-direction: row;
			  justify-content: center;
			  align-items: center;
			  
	    &:active {
	      transform: scale(0.95);
	    }
	
	    &.checked {
	      background: linear-gradient(135deg, #40B983 0%, #6BCF95 100%);
	      box-shadow: 0 4rpx 16rpx rgba(64, 185, 131, 0.3);
	    }
	
	    .btn-text {
	      font-size: 28rpx;
	      color: #fff;
	    }
	  }
	}
	
	.bottom-row{
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
		width: 100%;
	}

	.navs {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-top: 34rpx;
		padding: 0 30rpx;
		margin-bottom: 48rpx;

		.nav {
			display: flex;
			flex-direction: column;
			align-items: center;

			image {
				width: 52rpx;
				height: 52rpx;
				margin-bottom: 20rpx;
			}

			text {
				font-size: 26rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #2f465f;
				line-height: 26rpx;
			}
		}
	}
</style>